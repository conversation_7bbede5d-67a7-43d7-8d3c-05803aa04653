{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "paths": {"~/*": ["./src/*"], "@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/hooks/*": ["./src/lib/hooks/*"], "@/types/*": ["./src/types/*"], "@/utils/*": ["./src/utils/*"], "@/store/*": ["./src/lib/store/*"], "@/lib/*": ["./src/lib/*"]}}, "include": ["**/*.ts", "**/*.tsx", ".expo/types/**/*.ts", "expo-env.d.ts", "nativewind-env.d.ts"]}