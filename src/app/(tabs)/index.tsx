import React from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import {
  Activity,
  Heart,
  Droplets,
  Clock,
  TriangleAlert as AlertTriangle,
  TrendingUp,
  Calendar,
  Pill,
  Camera,
} from 'lucide-react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  useDailyProgress,
  useTodaysMedications,
  useMedicationActions,
  useThemeColors,
  useIsDarkTheme,
} from '@/lib/store/selectors';

interface DashboardCard {
  title: string;
  value: string;
  unit: string;
  icon: React.ReactNode;
  color: string;
  status: 'good' | 'warning' | 'danger';
}

export default function HomeScreen() {
  const colors = useThemeColors();
  const isDark = useIsDarkTheme();
  const dailyProgress = useDailyProgress();
  const todaysMedications = useTodaysMedications();
  const { markReminderTaken } = useMedicationActions();

  const dashboardCards: DashboardCard[] = [
    {
      title: 'Daily Sodium',
      value: dailyProgress.sodium.toString(),
      unit: 'mg',
      icon: <AlertTriangle size={24} color={colors.error} />,
      color: colors.error,
      status: dailyProgress.sodium > 2000 ? 'danger' : 'good',
    },
    {
      title: 'Protein Intake',
      value: dailyProgress.protein.toString(),
      unit: 'g',
      icon: <Activity size={24} color={colors.success} />,
      color: colors.success,
      status: 'good',
    },
    {
      title: 'Fat Intake',
      value: dailyProgress.fat.toString(),
      unit: 'g',
      icon: <Heart size={24} color={colors.warning} />,
      color: colors.warning,
      status: 'warning',
    },
    {
      title: 'Water Intake',
      value: dailyProgress.water.toString(),
      unit: 'glasses',
      icon: <Droplets size={24} color={colors.info} />,
      color: colors.info,
      status: 'good',
    },
  ];

  const handleMedicationToggle = (reminderId: string, taken: boolean) => {
    markReminderTaken(reminderId, !taken);
  };

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: colors.background }]}
    >
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={[styles.headerTitle, { color: colors.text }]}>
            Liver Health Dashboard
          </Text>
          <Text
            style={[styles.headerSubtitle, { color: colors.textSecondary }]}
          >
            Track your nutrition & wellness
          </Text>
        </View>

        {/* Daily Progress Cards */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Today&apos;s Progress
          </Text>
          <View style={styles.cardsGrid}>
            {dashboardCards.map(card => (
              <TouchableOpacity
                key={card.title}
                style={[styles.card, { backgroundColor: colors.surface }]}
              >
                <View style={styles.cardHeader}>
                  {card.icon}
                  <View
                    style={[
                      styles.statusIndicator,
                      { backgroundColor: card.color },
                    ]}
                  />
                </View>
                <Text
                  style={[styles.cardTitle, { color: colors.textSecondary }]}
                >
                  {card.title}
                </Text>
                <View style={styles.cardValueContainer}>
                  <Text style={[styles.cardValue, { color: card.color }]}>
                    {card.value}
                  </Text>
                  <Text
                    style={[styles.cardUnit, { color: colors.textSecondary }]}
                  >
                    {card.unit}
                  </Text>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Medication Reminders */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Pill size={20} color={colors.primary} />
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Medication Reminders
            </Text>
          </View>
          <View
            style={[styles.medicationList, { backgroundColor: colors.surface }]}
          >
            {todaysMedications.map(item => (
              <TouchableOpacity
                key={item.id}
                style={[
                  styles.medicationItem,
                  item.taken && {
                    backgroundColor: colors.surfaceSecondary,
                  },
                ]}
                onPress={() => handleMedicationToggle(item.id, item.taken)}
              >
                <View style={styles.medicationInfo}>
                  <Text
                    style={[
                      styles.medicationName,
                      { color: colors.text },
                      item.taken && {
                        textDecorationLine: 'line-through',
                        color: colors.textSecondary,
                      },
                    ]}
                  >
                    {item.medication?.name || 'Unknown Medication'}
                  </Text>
                  <View style={styles.medicationTimeContainer}>
                    <Clock size={14} color={colors.textSecondary} />
                    <Text
                      style={[
                        styles.medicationTime,
                        { color: colors.textSecondary },
                      ]}
                    >
                      {item.time}
                    </Text>
                  </View>
                </View>
                <View
                  style={[
                    styles.medicationCheckbox,
                    { borderColor: colors.border },
                    item.taken && {
                      backgroundColor: colors.success,
                      borderColor: colors.success,
                    },
                  ]}
                >
                  {item.taken && (
                    <Text
                      style={[styles.checkmark, { color: colors.textInverse }]}
                    >
                      ✓
                    </Text>
                  )}
                </View>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Quick Actions
          </Text>
          <View style={styles.quickActions}>
            {[
              {
                icon: <Camera size={24} color={colors.primary} />,
                text: 'Scan Food',
              },
              {
                icon: <Calendar size={24} color={colors.primary} />,
                text: 'Plan Meal',
              },
              {
                icon: <TrendingUp size={24} color={colors.primary} />,
                text: 'View Report',
              },
            ].map((action, index) => (
              <TouchableOpacity
                key={action.text}
                style={[
                  styles.actionButton,
                  { backgroundColor: colors.surface },
                ]}
              >
                {action.icon}
                <Text style={[styles.actionButtonText, { color: colors.text }]}>
                  {action.text}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Health Tip */}
        <View
          style={[
            styles.healthTip,
            {
              backgroundColor: isDark ? 'rgba(245, 158, 11, 0.1)' : '#FFFBEB',
              borderColor: isDark ? 'rgba(245, 158, 11, 0.3)' : '#FDE68A',
            },
          ]}
        >
          <AlertTriangle size={20} color={colors.warning} />
          <View style={styles.healthTipContent}>
            <Text style={[styles.healthTipTitle, { color: colors.warning }]}>
              Daily Tip
            </Text>
            <Text style={[styles.healthTipText, { color: colors.warning }]}>
              Limit sodium intake to less than 2000mg per day to reduce fluid
              retention.
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: 24,
  },
  headerTitle: {
    fontFamily: 'Inter-Bold',
    fontSize: 28,
    marginBottom: 4,
  },
  headerSubtitle: {
    fontFamily: 'Inter-Regular',
    fontSize: 16,
  },
  section: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 18,
    marginLeft: 8,
  },
  cardsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 12,
  },
  card: {
    width: '48%',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  cardTitle: {
    fontFamily: 'Inter-Medium',
    fontSize: 14,
    marginBottom: 8,
    textAlign: 'center',
  },
  cardValueContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
    justifyContent: 'center',
  },
  cardValue: {
    fontFamily: 'Inter-Bold',
    fontSize: 20,
  },
  cardUnit: {
    fontFamily: 'Inter-Regular',
    fontSize: 12,
    marginLeft: 4,
  },
  medicationList: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  medicationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  medicationInfo: {
    flex: 1,
  },
  medicationName: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 16,
    marginBottom: 4,
  },
  medicationTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  medicationTime: {
    fontFamily: 'Inter-Regular',
    fontSize: 14,
    marginLeft: 6,
  },
  medicationCheckbox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkmark: {
    fontFamily: 'Inter-Bold',
    fontSize: 12,
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flex: 1,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    marginHorizontal: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  actionButtonText: {
    fontFamily: 'Inter-Medium',
    fontSize: 12,
    marginTop: 8,
    textAlign: 'center',
  },
  healthTip: {
    flexDirection: 'row',
    margin: 20,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
  },
  healthTipContent: {
    flex: 1,
    marginLeft: 12,
  },
  healthTipTitle: {
    fontFamily: 'Inter-SemiBold',
    fontSize: 14,
    marginBottom: 4,
  },
  healthTipText: {
    fontFamily: 'Inter-Regular',
    fontSize: 14,
    lineHeight: 20,
  },
});
