import * as React from 'react';
import {
  View,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Alert,
} from 'react-native';
import { isClerkAPIResponseError, useSignUp } from '@clerk/clerk-expo';
import { Link, useRouter } from 'expo-router';
import { useSignUpForm } from '@/lib/hooks/useSignUpForm';
import { Controller } from 'react-hook-form';
import { AlertCircle } from 'lucide-react-native';
import Dropdown from '@/components/onboarding/Dropdown';
import { ROLE_OPTIONS, SignUpFormData } from '@/types/auth';
import { ClerkAPIError } from '@clerk/types';
import { Button } from '@/components/ui/button';
import { Text } from '@/components/ui/text';
import { Input } from '@/components/ui/input';

export default function SignUpScreen() {
  const router = useRouter();
  const { isLoaded, signUp } = useSignUp();

  const {
    watch,
    control,
    setValue,
    trigger,
    loading,
    // onSubmit,
    handleSubmit,
    formState: { errors, isValid },
  } = useSignUpForm();

  // Watch form values for real-time updates
  const watchedValues = watch();

  const [clerkErrors, setClerkErrors] = React.useState<ClerkAPIError[]>([]);
  const [pendingVerification, setPendingVerification] = React.useState(false);

  // Handle submission of sign-up form
  const onSignUpPress = React.useCallback(
    async (data: SignUpFormData) => {
      if (!isLoaded) return;

      // Clear any errors that may have occurred during previous form submission
      setClerkErrors([]);

      const { emailAddress, password } = data;

      // Start sign-up process using email and password provided
      try {
        const signUpAttempt = await signUp.create({
          emailAddress,
          password,
        });

        // Send user an email with verification code
        await signUp.prepareEmailAddressVerification({
          strategy: 'email_code',
        });

        // Set 'pendingVerification' to true to display second form
        // and capture OTP code
        setPendingVerification(true);

        if (signUpAttempt.status === 'complete') {
          Alert.alert(
            'Success',
            'Account created! Please check your email for verification.',
            [
              {
                text: 'OK',
                onPress: () => router.replace('/(auth)/sign-in'),
              },
            ]
          );
        }
      } catch (err) {
        // See https://clerk.com/docs/custom-flows/error-handling
        // for more info on error handling
        console.error(JSON.stringify(err, null, 2));
        Alert.alert('Error', 'An unexpected error occurred');

        if (isClerkAPIResponseError(err)) setClerkErrors(err.errors);
        console.error(JSON.stringify(err, null, 2));
      }
    },
    [isLoaded, router, signUp]
  );

  if (pendingVerification) {
    return <VerifyEmailScreen />;
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView style={styles.scrollView} keyboardShouldPersistTaps="handled">
        <View style={styles.content}>
          <Text style={styles.title}>Create Account</Text>
          <Text style={styles.subtitle}>
            Join us in your liver health journey
          </Text>

          <Text style={styles.label}>
            Full Name
            <Text style={styles.required}> *</Text>
          </Text>
          <View
            style={[
              styles.inputContainer,
              errors.fullName && styles.inputError,
            ]}
          >
            <Controller
              name="fullName"
              control={control}
              rules={{ required: true }}
              render={({ field: { onChange, onBlur, value } }) => (
                <TextInput
                  style={styles.input}
                  placeholder="Full Name"
                  placeholderTextColor={colors.textTertiary}
                  value={value}
                  onBlur={onBlur}
                  onChangeText={onChange}
                  autoCapitalize="words"
                  autoComplete="name"
                  accessibilityLabel="Full name input"
                />
              )}
            />
          </View>
          {errors.fullName && (
            <View style={styles.errorContainer}>
              <AlertCircle size={16} color={colors.error} />
              <Text style={styles.errorText}>{errors.fullName.message}</Text>
            </View>
          )}

          <Text style={styles.label}>
            Email Address
            <Text style={styles.required}> *</Text>
          </Text>
          <View
            style={[
              styles.inputContainer,
              errors.emailAddress && styles.inputError,
            ]}
          >
            <TextInput
              style={styles.input}
              placeholder="Email Address"
              placeholderTextColor={colors.textTertiary}
              value={watchedValues.emailAddress || ''}
              onChangeText={text => setValue('emailAddress', text)}
              onBlur={() => trigger('emailAddress')}
              keyboardType="email-address"
              autoCapitalize="none"
              autoComplete="email"
              accessibilityLabel="Email address input"
            />
          </View>
          {errors.emailAddress && (
            <View style={styles.errorContainer}>
              <AlertCircle size={16} color={colors.error} />
              <Text style={styles.errorText}>
                {errors.emailAddress.message}
              </Text>
            </View>
          )}

          <Dropdown
            label="Role"
            value={watchedValues.role || 'patient'}
            options={[...ROLE_OPTIONS]}
            onSelect={value =>
              setValue(
                'role',
                value as 'patient' | 'caregiver' | 'healthcare_provider'
              )
            }
            placeholder="Select your role"
            error={errors.role?.message}
            required
            accessibilityLabel="Role selection dropdown"
          />

          <Text style={styles.label}>
            Password
            <Text style={styles.required}> *</Text>
          </Text>
          <View
            style={[
              styles.inputContainer,
              errors.password && styles.inputError,
            ]}
          >
            <TextInput
              style={styles.input}
              placeholder="Password"
              placeholderTextColor={colors.textTertiary}
              value={watchedValues.password || ''}
              onChangeText={text => setValue('password', text)}
              onBlur={() => trigger('password')}
              secureTextEntry
              autoComplete="new-password"
              accessibilityLabel="Password input"
            />
          </View>
          {errors.password && (
            <View style={styles.errorContainer}>
              <AlertCircle size={16} color={colors.error} />
              <Text style={styles.errorText}>{errors.password.message}</Text>
            </View>
          )}

          <Text style={styles.label}>
            Confirm Password
            <Text style={styles.required}> *</Text>
          </Text>
          <View
            style={[
              styles.inputContainer,
              errors.confirmPassword && styles.inputError,
            ]}
          >
            <TextInput
              style={styles.input}
              placeholder="Confirm Password"
              placeholderTextColor={colors.textTertiary}
              value={watchedValues.confirmPassword || ''}
              onChangeText={text => setValue('confirmPassword', text)}
              onBlur={() => trigger('confirmPassword')}
              secureTextEntry
              autoComplete="new-password"
              accessibilityLabel="Confirm password input"
            />
          </View>
          {errors.confirmPassword && (
            <View style={styles.errorContainer}>
              <AlertCircle size={16} color={colors.error} />
              <Text style={styles.errorText}>
                {errors.confirmPassword.message}
              </Text>
            </View>
          )}

          <TouchableOpacity
            style={[
              styles.button,
              (loading || !isValid) && styles.buttonDisabled,
            ]}
            onPress={handleSubmit(onSignUpPress)}
            disabled={loading || !isValid}
          >
            <Text style={styles.buttonText}>
              {loading ? 'Creating Account...' : 'Create Account'}
            </Text>
          </TouchableOpacity>

          <View style={styles.footer}>
            <Text style={styles.footerText}>Already have an account? </Text>
            <Link href="/(auth)/sign-in" asChild>
              <TouchableOpacity>
                <Text style={styles.link}>Sign In</Text>
              </TouchableOpacity>
            </Link>
          </View>

          {clerkErrors.length > 0 && (
            <View style={styles.errorContainer}>
              <AlertCircle size={16} color={colors.error} />
              <Text style={styles.errorText}>
                {clerkErrors.map(el => (
                  <li key={el.code}>{el.longMessage}</li>
                ))}
              </Text>
            </View>
          )}
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

function VerifyEmailScreen() {
  const router = useRouter();
  const colors = useThemeColors();
  const styles = useLocalStyles();
  const { isLoaded, signUp, setActive } = useSignUp();

  const [code, setCode] = React.useState('');

  // Handle submission of verification form
  const onVerifyPress = React.useCallback(async () => {
    if (!isLoaded) return;

    try {
      // Use the code the user provided to attempt verification
      const signUpAttempt = await signUp.attemptEmailAddressVerification({
        code,
      });

      // If verification was completed, set the session to active
      // and redirect the user
      if (signUpAttempt.status === 'complete') {
        await setActive({ session: signUpAttempt.createdSessionId });
        router.replace('/');
      } else {
        // If the status is not complete, check why. User may need to
        // complete further steps.
        console.error(JSON.stringify(signUpAttempt, null, 2));
      }
    } catch (err) {
      // See https://clerk.com/docs/custom-flows/error-handling
      // for more info on error handling
      console.error(JSON.stringify(err, null, 2));
    }
  }, [isLoaded, signUp, code, setActive, router]);

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.content}>
          <Text style={styles.title}>Verify Your Email</Text>
          <Text style={styles.subtitle}>
            We&apos;ve sent a verification code to your email address. Please
            enter it below to complete your account setup.
          </Text>

          <Text style={styles.label}>
            Verification Code
            <Text style={styles.required}> *</Text>
          </Text>
          <View style={styles.inputContainer}>
            <TextInput
              style={styles.input}
              value={code}
              placeholder="Enter your verification code"
              placeholderTextColor={colors.textTertiary}
              onChangeText={code => setCode(code)}
              keyboardType="number-pad"
              autoComplete="one-time-code"
              accessibilityLabel="Verification code input"
            />
          </View>

          <TouchableOpacity
            style={[styles.button, !code && styles.buttonDisabled]}
            onPress={onVerifyPress}
            disabled={!code}
          >
            <Text style={styles.buttonText}>Verify Email</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

function useLocalStyles() {
  const colors = useThemeColors();

  const styles = React.useMemo(() => {
    return StyleSheet.create({
      container: {
        flex: 1,
        backgroundColor: colors.background,
      },
      scrollView: {
        flex: 1,
      },
      content: {
        paddingHorizontal: 24,
        paddingVertical: 40,
      },
      title: {
        fontSize: 32,
        fontFamily: 'Inter-Bold',
        color: colors.text,
        marginBottom: 8,
        textAlign: 'center',
      },
      subtitle: {
        fontSize: 16,
        fontFamily: 'Inter-Regular',
        color: colors.textSecondary,
        marginBottom: 32,
        textAlign: 'center',
      },
      inputContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        borderWidth: 1,
        borderRadius: 8,
        paddingHorizontal: 12,
        minHeight: 48,
        backgroundColor: colors.surface,
        borderColor: colors.border,
        marginBottom: 20,
      },
      input: {
        flex: 1,
        fontFamily: 'Inter-Regular',
        fontSize: 16,
        paddingVertical: 12,
        color: colors.text,
      },
      inputError: {
        borderColor: colors.error,
        borderWidth: 1,
      },
      label: {
        fontFamily: 'Inter-Medium',
        fontSize: 16,
        marginBottom: 8,
        color: colors.text,
      },
      required: {
        fontFamily: 'Inter-Medium',
        fontSize: 16,
        color: colors.error,
      },
      errorContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: -12,
        marginBottom: 20,
      },
      errorText: {
        fontFamily: 'Inter-Regular',
        fontSize: 14,
        marginLeft: 6,
        flex: 1,
        color: colors.error,
      },
      button: {
        backgroundColor: colors.primary,
        borderRadius: 8,
        padding: 16,
        alignItems: 'center',
        marginTop: 12,
        marginBottom: 24,
      },
      buttonDisabled: {
        opacity: 0.6,
      },
      buttonText: {
        color: 'white',
        fontFamily: 'Inter-SemiBold',
        fontSize: 16,
      },
      footer: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        marginTop: 8,
      },
      footerText: {
        color: colors.textSecondary,
        fontFamily: 'Inter-Regular',
        fontSize: 14,
      },
      link: {
        color: colors.primary,
        fontFamily: 'Inter-SemiBold',
        fontSize: 14,
      },
    });
  }, [colors]);

  return styles;
}
