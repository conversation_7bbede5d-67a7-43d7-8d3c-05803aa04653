import React from 'react';
import { Stack, Redirect } from 'expo-router';
import { useAuth } from '@/lib/hooks/useAuth';
import { useThemeColors } from '@/lib/store/selectors';

export default function OnboardingRoutesLayout() {
  const { user, isSignedIn, isLoaded } = useAuth();
  const colors = useThemeColors();

  // If not loaded yet, don't render anything (loading will be handled by parent)
  if (!isLoaded) {
    return null;
  }

  // If not signed in, redirect to auth
  if (!isSignedIn || !user) {
    return <Redirect href="/(auth)/sign-in" />;
  }

  // If onboarding is already completed, redirect to main app
  if (user.onboarding_completed) {
    return <Redirect href="/(tabs)" />;
  }

  return (
    <Stack
      screenOptions={{
        headerStyle: {
          backgroundColor: colors.background,
        },
        headerTintColor: colors.text,
        headerTitleStyle: {
          fontWeight: '600',
        },
        headerShadowVisible: false,
        contentStyle: {
          backgroundColor: colors.background,
        },
      }}
    >
      <Stack.Screen
        name="index"
        options={{
          title: 'Setup Your Profile',
          headerShown: false,
        }}
      />
    </Stack>
  );
}
