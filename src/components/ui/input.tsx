import * as React from 'react';
import { TextInput, View } from 'react-native';
import { cn } from '@/lib/utils';
import { Text } from '@/components/ui/text';

export interface InputProps extends React.ComponentProps<typeof TextInput> {
  label?: string;
  error?: string;
  required?: boolean;
  className?: string;
  containerClassName?: string;
}

const Input = React.forwardRef<
  React.ElementRef<typeof TextInput>,
  InputProps
>(({ className, label, error, required, containerClassName, ...props }, ref) => {
  return (
    <View className={cn('mb-4', containerClassName)}>
      {label && (
        <View className="flex-row items-center mb-2">
          <Text className="text-base font-medium text-foreground">
            {label}
          </Text>
          {required && (
            <Text className="text-base font-medium text-destructive ml-1">
              *
            </Text>
          )}
        </View>
      )}
      <TextInput
        className={cn(
          'bg-card border border-input rounded-lg px-4 py-3 text-base text-foreground',
          'placeholder:text-muted-foreground',
          'focus:border-ring focus:ring-2 focus:ring-ring/20',
          error && 'border-destructive',
          className
        )}
        ref={ref}
        {...props}
      />
      {error && (
        <Text className="text-sm text-destructive mt-1">
          {error}
        </Text>
      )}
    </View>
  );
});

Input.displayName = 'Input';

export { Input };
